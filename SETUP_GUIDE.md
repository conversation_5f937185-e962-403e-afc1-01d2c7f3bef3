# 人事管理系统 - 安装和使用指南

## 系统概述

这是一个基于Java C/S架构的简单人事管理系统，使用JDK8+MySQL8+控制台交互实现。

### 功能特性
- **人事经理功能**：添加/删除操作员、查看所有系统事件、系统事件查询
- **操作员功能**：查询操作员相关的系统事件
- **登录功能**：用户名密码验证、密码修改功能

## 安装步骤

### 1. 环境准备

确保您的系统已安装：
- **JDK 8或更高版本**
- **MySQL 8.0或更高版本**

### 2. 数据库设置

1. 启动MySQL服务
2. 使用MySQL客户端执行数据库初始化：
```bash
mysql -u root -p < sql/init.sql
```

或者手动执行sql/init.sql中的SQL语句。

### 3. 下载MySQL驱动

运行以下脚本自动下载MySQL驱动：
```bash
download_mysql_driver.bat
```

如果自动下载失败，请手动下载：
- 访问：https://dev.mysql.com/downloads/connector/j/
- 下载：mysql-connector-j-8.0.33.jar
- 保存到：lib/mysql-connector-java-8.0.33.jar

### 4. 编译项目

```bash
compile.bat
```

## 使用方法

### 1. 启动服务端

```bash
start_server.bat
```

服务端将在端口8888上监听客户端连接。

### 2. 启动客户端

```bash
start_client.bat
```

### 3. 登录系统

系统提供一个默认的管理员账户：
- **用户名**：admin
- **密码**：admin123
- **角色**：人事经理

## 功能说明

### 人事经理功能

1. **添加操作员**
   - 输入新操作员的用户名和密码
   - 系统会检查用户名是否已存在

2. **删除操作员**
   - 输入要删除的操作员用户名
   - 确认删除操作

3. **查看所有系统事件**
   - 显示系统中所有的操作记录
   - 包括时间、事件描述、操作者信息

4. **系统事件查询**
   - 可选择查询人事经理事件或操作员事件
   - 按角色分类显示事件

### 操作员功能

1. **系统事件查询**
   - 只能查看操作员相关的系统事件
   - 无法查看人事经理的操作记录

### 登录功能

1. **正常登录**
   - 用户名和密码正确时直接登录

2. **用户不存在**
   - 显示"用户不存在"提示

3. **密码错误处理**
   - 提示密码错误
   - 提供修改密码选项
   - 修改成功后需要重新登录

## 配置说明

### 数据库配置

如需修改数据库连接信息，请编辑 `server/DatabaseManager.java`：

```java
private static final String DB_URL = "********************************************************************************************";
private static final String DB_USER = "root";
private static final String DB_PASSWORD = "root";
```

### 网络配置

默认服务端端口为8888，如需修改：
- 编辑 `server/Server.java` 中的 `PORT` 常量
- 编辑 `client/NetworkManager.java` 中的 `SERVER_PORT` 常量

## 故障排除

### 编译问题

1. **找不到MySQL驱动**
   - 确保lib目录下有mysql-connector-java-8.0.33.jar文件
   - 运行download_mysql_driver.bat下载驱动

2. **编码问题**
   - 确保使用UTF-8编码保存Java文件
   - 如果仍有问题，请使用英文注释

### 运行问题

1. **无法连接数据库**
   - 检查MySQL服务是否启动
   - 验证数据库连接信息是否正确
   - 确保hr_system数据库已创建

2. **客户端无法连接服务端**
   - 确保服务端已启动
   - 检查防火墙设置
   - 验证端口8888是否被占用

3. **登录失败**
   - 使用默认账户：admin/admin123
   - 检查数据库中users表是否有数据

## 项目结构

```
├── server/                 # 服务端代码
│   ├── Server.java        # 服务端主类
│   ├── ServerHandler.java # 请求处理器
│   ├── DatabaseManager.java # 数据库管理
│   ├── User.java          # 用户实体类
│   └── SystemEvent.java   # 系统事件实体类
├── client/                # 客户端代码
│   ├── Client.java        # 客户端主类
│   └── NetworkManager.java # 网络通信管理
├── sql/                   # 数据库脚本
│   └── init.sql          # 数据库初始化脚本
├── lib/                   # 依赖库目录
├── bin/                   # 编译输出目录
├── compile.bat           # 编译脚本
├── start_server.bat      # 启动服务端脚本
├── start_client.bat      # 启动客户端脚本
└── download_mysql_driver.bat # MySQL驱动下载脚本
```

## 注意事项

1. 必须先启动服务端，再启动客户端
2. 确保MySQL服务正在运行
3. 首次使用前必须执行数据库初始化脚本
4. 系统会自动记录所有操作的系统事件
5. 人事经理可以管理操作员，操作员只能查询自己相关的事件
