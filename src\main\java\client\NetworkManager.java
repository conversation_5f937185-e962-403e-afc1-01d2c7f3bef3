package client;

import server.SystemEvent;
import java.io.*;
import java.net.Socket;
import java.util.ArrayList;
import java.util.List;

/**
 * Network Communication Manager
 */
public class NetworkManager {
    private static final String SERVER_HOST = "localhost";
    private static final int SERVER_PORT = 8888;
    
    private Socket socket;
    private ObjectOutputStream out;
    private ObjectInputStream in;
    
    /**
     * Connect to server
     */
    public boolean connect() {
        try {
            socket = new Socket(SERVER_HOST, SERVER_PORT);
            out = new ObjectOutputStream(socket.getOutputStream());
            in = new ObjectInputStream(socket.getInputStream());
            return true;
        } catch (IOException e) {
            System.err.println("Failed to connect to server: " + e.getMessage());
            return false;
        }
    }

    /**
     * Disconnect from server
     */
    public void disconnect() {
        try {
            if (socket != null && !socket.isClosed()) {
                socket.close();
            }
        } catch (IOException e) {
            System.err.println("Error disconnecting: " + e.getMessage());
        }
    }
    
    /**
     * User login
     */
    public String login(String username, String password) {
        try {
            out.writeObject("LOGIN|" + username + "|" + password);
            String response = (String) in.readObject();
            return response;
        } catch (Exception e) {
            System.err.println("Login request failed: " + e.getMessage());
            return "ERROR";
        }
    }

    /**
     * Check if user exists
     */
    public boolean checkUserExists(String username) {
        try {
            out.writeObject("CHECK_USER|" + username);
            String response = (String) in.readObject();
            String[] parts = response.split("\\|");
            return Boolean.parseBoolean(parts[1]);
        } catch (Exception e) {
            System.err.println("Check user failed: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Update password
     */
    public boolean updatePassword(String username, String newPassword) {
        try {
            out.writeObject("UPDATE_PASSWORD|" + username + "|" + newPassword);
            String response = (String) in.readObject();
            String[] parts = response.split("\\|");
            return "SUCCESS".equals(parts[1]);
        } catch (Exception e) {
            System.err.println("Update password failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Add operator
     */
    public String addOperator(String username, String password, String managerName) {
        try {
            out.writeObject("ADD_OPERATOR|" + username + "|" + password + "|" + managerName);
            String response = (String) in.readObject();
            String[] parts = response.split("\\|");
            return parts[1];
        } catch (Exception e) {
            System.err.println("Add operator failed: " + e.getMessage());
            return "ERROR";
        }
    }
    
    /**
     * Delete operator
     */
    public boolean deleteOperator(String username, String managerName) {
        try {
            out.writeObject("DELETE_OPERATOR|" + username + "|" + managerName);
            String response = (String) in.readObject();
            String[] parts = response.split("\\|");
            return "SUCCESS".equals(parts[1]);
        } catch (Exception e) {
            System.err.println("Delete operator failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Get all system events
     */
    public List<SystemEvent> getAllSystemEvents() {
        try {
            out.writeObject("GET_ALL_EVENTS");
            String response = (String) in.readObject();
            String[] parts = response.split("\\|");
            int count = Integer.parseInt(parts[1]);

            List<SystemEvent> events = new ArrayList<>();
            for (int i = 0; i < count; i++) {
                SystemEvent event = (SystemEvent) in.readObject();
                events.add(event);
            }
            return events;
        } catch (Exception e) {
            System.err.println("Get system events failed: " + e.getMessage());
            return new ArrayList<>();
        }
    }
    
    /**
     * Get system events by role
     */
    public List<SystemEvent> getSystemEventsByRole(String role) {
        try {
            out.writeObject("GET_EVENTS_BY_ROLE|" + role);
            String response = (String) in.readObject();
            String[] parts = response.split("\\|");
            int count = Integer.parseInt(parts[1]);

            List<SystemEvent> events = new ArrayList<>();
            for (int i = 0; i < count; i++) {
                SystemEvent event = (SystemEvent) in.readObject();
                events.add(event);
            }
            return events;
        } catch (Exception e) {
            System.err.println("Get system events failed: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * Get operator events
     */
    public List<SystemEvent> getOperatorEvents() {
        try {
            out.writeObject("GET_OPERATOR_EVENTS");
            String response = (String) in.readObject();
            String[] parts = response.split("\\|");
            int count = Integer.parseInt(parts[1]);

            List<SystemEvent> events = new ArrayList<>();
            for (int i = 0; i < count; i++) {
                SystemEvent event = (SystemEvent) in.readObject();
                events.add(event);
            }
            return events;
        } catch (Exception e) {
            System.err.println("Get operator events failed: " + e.getMessage());
            return new ArrayList<>();
        }
    }
}
