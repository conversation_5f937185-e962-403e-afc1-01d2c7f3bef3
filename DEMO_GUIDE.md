# 人事管理系统 - 演示指南

## 系统演示步骤

### 准备工作

1. **确保MySQL服务运行**
2. **执行数据库初始化**
   ```bash
   mysql -u root -p < sql/init.sql
   ```
3. **编译项目**
   ```bash
   compile.bat
   ```

### 演示流程

#### 第一步：启动服务端

1. 打开第一个命令行窗口
2. 运行：`start_server.bat`
3. 看到以下输出表示服务端启动成功：
   ```
   HR Management System Server started, listening on port: 8888
   Waiting for client connections...
   ```

#### 第二步：启动客户端

1. 打开第二个命令行窗口
2. 运行：`start_client.bat`
3. 看到以下输出：
   ```
   === HR Management System Client ===
   Connected to server
   
   === Login ===
   Enter username:
   ```

#### 第三步：人事经理登录演示

1. **输入默认管理员账户**
   ```
   Enter username: admin
   Enter password: admin123
   ```

2. **登录成功后显示管理员菜单**
   ```
   Login successful! Welcome, Manager admin
   
   === Manager Menu ===
   1. Add Operator
   2. Delete Operator
   3. View All System Events
   4. Query System Events
   0. Exit
   Please select operation:
   ```

#### 第四步：添加操作员演示

1. **选择选项1 - Add Operator**
   ```
   Please select operation: 1
   
   === Add Operator ===
   Enter operator username: operator1
   Enter operator password: pass123
   Operator added successfully!
   ```

2. **再添加一个操作员**
   ```
   Enter operator username: operator2
   Enter operator password: pass456
   Operator added successfully!
   ```

#### 第五步：查看系统事件演示

1. **选择选项3 - View All System Events**
   ```
   Please select operation: 3
   
   === All System Events ===
   Found 3 system events:
   ----------------------------------------
   [2025-06-15 18:30:15.0] Add operator: operator2 (Operator: admin - manager)
   [2025-06-15 18:30:10.0] Add operator: operator1 (Operator: admin - manager)
   [2025-06-15 18:25:00.0] User login (Operator: admin - manager)
   ```

#### 第六步：系统事件查询演示

1. **选择选项4 - Query System Events**
   ```
   Please select operation: 4
   
   === System Events Query ===
   1. Query Manager Events
   2. Query Operator Events
   0. Return
   Please select query type: 1
   
   === Manager Events ===
   Found 3 manager events:
   ----------------------------------------
   [2025-06-15 18:30:15.0] Add operator: operator2 (Operator: admin - manager)
   [2025-06-15 18:30:10.0] Add operator: operator1 (Operator: admin - manager)
   [2025-06-15 18:25:00.0] User login (Operator: admin - manager)
   ```

#### 第七步：删除操作员演示

1. **选择选项2 - Delete Operator**
   ```
   Please select operation: 2
   
   === Delete Operator ===
   Enter username to delete: operator2
   Confirm delete operator operator2? (y/n): y
   Operator deleted successfully!
   ```

#### 第八步：操作员登录演示

1. **退出管理员账户**
   ```
   Please select operation: 0
   Disconnected, program ended.
   ```

2. **重新启动客户端并用操作员登录**
   ```
   start_client.bat
   
   === Login ===
   Enter username: operator1
   Enter password: pass123
   
   Login successful! Welcome, Operator operator1
   
   === Operator Menu ===
   1. Query System Events (Operator Events)
   0. Exit
   Please select operation:
   ```

3. **查看操作员事件**
   ```
   Please select operation: 1
   
   === Operator System Events ===
   Found 0 operator events:
   ----------------------------------------
   ```

#### 第九步：密码修改功能演示

1. **故意输入错误密码**
   ```
   Enter username: operator1
   Enter password: wrongpass
   Password incorrect!
   Do you want to change password? (y/n): y
   Enter new password: newpass123
   Password updated successfully! Please login again.
   ```

2. **使用新密码登录**
   ```
   Enter username: operator1
   Enter password: newpass123
   Login successful! Welcome, Operator operator1
   ```

## 演示要点

### 功能验证

✅ **登录验证**
- 正确用户名密码登录成功
- 错误用户名提示"用户不存在"
- 错误密码可选择修改密码

✅ **角色权限**
- 人事经理可以添加/删除操作员
- 人事经理可以查看所有系统事件
- 操作员只能查看操作员相关事件

✅ **系统事件**
- 所有操作都会被记录
- 包含时间戳、事件描述、操作者信息
- 支持按角色查询

✅ **网络通信**
- 客户端服务端正常通信
- 支持多客户端连接
- 连接断开处理正常

### 演示亮点

1. **简洁的架构设计** - 清晰的C/S分离
2. **完整的功能实现** - 满足所有原始需求
3. **良好的用户体验** - 直观的控制台交互
4. **严格的权限控制** - 角色权限分离明确
5. **完善的日志记录** - 所有操作可追溯

## 常见问题处理

### 连接问题
- 确保服务端先启动
- 检查端口8888是否被占用
- 验证防火墙设置

### 数据库问题
- 确保MySQL服务运行
- 验证数据库hr_system是否存在
- 检查用户权限设置

### 编译问题
- 确保JDK版本8+
- 验证MySQL驱动是否下载
- 检查JAVA_HOME环境变量

## 演示总结

本系统成功实现了一个功能完整、架构清晰的人事管理系统，完全满足C/S架构+JDK8+MySQL8+控制台交互的要求。系统具有良好的扩展性和维护性，是一个优秀的学习和演示项目。
