package server;

import java.io.*;
import java.net.Socket;
import java.util.List;

/**
 * 服务器请求处理器
 */
public class Server<PERSON><PERSON><PERSON> extends Thread {
    private Socket clientSocket;
    private ObjectInputStream in;
    private ObjectOutputStream out;
    
    public ServerHandler(Socket clientSocket) {
        this.clientSocket = clientSocket;
        try {
            out = new ObjectOutputStream(clientSocket.getOutputStream());
            in = new ObjectInputStream(clientSocket.getInputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    
    @Override
    public void run() {
        try {
            String request;
            while ((request = (String) in.readObject()) != null) {
                System.out.println("收到请求: " + request);
                handleRequest(request);
            }
        } catch (Exception e) {
            System.out.println("客户端断开连接");
        } finally {
            try {
                clientSocket.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
    
    private void handleRequest(String request) throws IOException {
        String[] parts = request.split("\\|");
        String command = parts[0];
        
        try {
            switch (command) {
                case "LOGIN":
                    handleLogin(parts[1], parts[2]);
                    break;
                case "CHECK_USER":
                    handleCheckUser(parts[1]);
                    break;
                case "UPDATE_PASSWORD":
                    handleUpdatePassword(parts[1], parts[2]);
                    break;
                case "ADD_OPERATOR":
                    handleAddOperator(parts[1], parts[2], parts[3]);
                    break;
                case "DELETE_OPERATOR":
                    handleDeleteOperator(parts[1], parts[2]);
                    break;
                case "GET_ALL_EVENTS":
                    handleGetAllEvents();
                    break;
                case "GET_EVENTS_BY_ROLE":
                    handleGetEventsByRole(parts[1]);
                    break;
                case "GET_OPERATOR_EVENTS":
                    handleGetOperatorEvents();
                    break;
                default:
                    out.writeObject("ERROR|未知命令");
                    break;
            }
        } catch (Exception e) {
            out.writeObject("ERROR|服务器内部错误: " + e.getMessage());
        }
    }
    
    private void handleLogin(String username, String password) throws IOException {
        User user = DatabaseManager.authenticateUser(username, password);
        if (user != null) {
            DatabaseManager.logSystemEvent("用户登录", username, user.getRole());
            out.writeObject("LOGIN_SUCCESS|" + user.getRole());
        } else {
            out.writeObject("LOGIN_FAILED");
        }
    }
    
    private void handleCheckUser(String username) throws IOException {
        boolean exists = DatabaseManager.userExists(username);
        out.writeObject("USER_EXISTS|" + exists);
    }
    
    private void handleUpdatePassword(String username, String newPassword) throws IOException {
        boolean success = DatabaseManager.updatePassword(username, newPassword);
        if (success) {
            DatabaseManager.logSystemEvent("密码修改", username, "unknown");
            out.writeObject("PASSWORD_UPDATED|SUCCESS");
        } else {
            out.writeObject("PASSWORD_UPDATED|FAILED");
        }
    }
    
    private void handleAddOperator(String username, String password, String managerName) throws IOException {
        if (DatabaseManager.userExists(username)) {
            out.writeObject("ADD_OPERATOR|USER_EXISTS");
        } else {
            boolean success = DatabaseManager.addOperator(username, password);
            if (success) {
                DatabaseManager.logSystemEvent("添加操作员: " + username, managerName, "manager");
                out.writeObject("ADD_OPERATOR|SUCCESS");
            } else {
                out.writeObject("ADD_OPERATOR|FAILED");
            }
        }
    }
    
    private void handleDeleteOperator(String username, String managerName) throws IOException {
        boolean success = DatabaseManager.deleteOperator(username);
        if (success) {
            DatabaseManager.logSystemEvent("删除操作员: " + username, managerName, "manager");
            out.writeObject("DELETE_OPERATOR|SUCCESS");
        } else {
            out.writeObject("DELETE_OPERATOR|FAILED");
        }
    }
    
    private void handleGetAllEvents() throws IOException {
        List<SystemEvent> events = DatabaseManager.getAllSystemEvents();
        out.writeObject("EVENTS|" + events.size());
        for (SystemEvent event : events) {
            out.writeObject(event);
        }
    }
    
    private void handleGetEventsByRole(String role) throws IOException {
        List<SystemEvent> events = DatabaseManager.getSystemEventsByRole(role);
        out.writeObject("EVENTS|" + events.size());
        for (SystemEvent event : events) {
            out.writeObject(event);
        }
    }
    
    private void handleGetOperatorEvents() throws IOException {
        List<SystemEvent> events = DatabaseManager.getOperatorEvents();
        out.writeObject("EVENTS|" + events.size());
        for (SystemEvent event : events) {
            out.writeObject(event);
        }
    }
}
