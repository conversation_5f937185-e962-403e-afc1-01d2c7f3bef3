package client;

import server.SystemEvent;
import java.util.List;
import java.util.Scanner;

/**
 * 客户端
 */
public class Client {
    private NetworkManager networkManager;
    private Scanner scanner;
    private String currentUser;
    private String currentRole;
    
    public Client() {
        networkManager = new NetworkManager();
        scanner = new Scanner(System.in);
    }
    
    public void start() {
        System.out.println("=== 人事管理系统客户端 ===");

        // 连接服务器
        if (!networkManager.connect()) {
            System.out.println("无法连接到服务器，程序退出。");
            return;
        }

        System.out.println("已连接到服务器");

        // 登录流程
        if (login()) {
            // 根据角色显示不同菜单
            if ("manager".equals(currentRole)) {
                managerMenu();
            } else if ("operator".equals(currentRole)) {
                operatorMenu();
            }
        }

        networkManager.disconnect();
        System.out.println("已断开连接，程序结束。");
    }
    
    /**
     * 登录流程
     */
    private boolean login() {
        while (true) {
            System.out.println("\n=== 登录 ===");
            System.out.print("请输入用户名: ");
            String username = scanner.nextLine().trim();

            if (username.isEmpty()) {
                System.out.println("用户名不能为空！");
                continue;
            }

            System.out.print("请输入密码: ");
            String password = scanner.nextLine().trim();

            if (password.isEmpty()) {
                System.out.println("密码不能为空！");
                continue;
            }

            // 尝试登录
            String loginResult = networkManager.login(username, password);

            if (loginResult.startsWith("LOGIN_SUCCESS")) {
                String[] parts = loginResult.split("\\|");
                currentUser = username;
                currentRole = parts[1];
                System.out.println("登录成功！欢迎您，" +
                    ("manager".equals(currentRole) ? "人事经理" : "操作员") + " " + username);
                return true;
            } else {
                // 登录失败，检查用户是否存在
                boolean userExists = networkManager.checkUserExists(username);

                if (!userExists) {
                    System.out.println("用户不存在！");
                } else {
                    System.out.println("密码错误！");
                    System.out.print("是否要修改密码？(y/n): ");
                    String choice = scanner.nextLine().trim().toLowerCase();

                    if ("y".equals(choice) || "yes".equals(choice)) {
                        System.out.print("请输入新密码: ");
                        String newPassword = scanner.nextLine().trim();

                        if (!newPassword.isEmpty()) {
                            if (networkManager.updatePassword(username, newPassword)) {
                                System.out.println("密码修改成功！请重新登录。");
                            } else {
                                System.out.println("密码修改失败！");
                            }
                        } else {
                            System.out.println("新密码不能为空！");
                        }
                    }
                }

                System.out.print("是否继续登录？(y/n): ");
                String continueChoice = scanner.nextLine().trim().toLowerCase();
                if (!"y".equals(continueChoice) && !"yes".equals(continueChoice)) {
                    return false;
                }
            }
        }
    }
    
    /**
     * 人事经理菜单
     */
    private void managerMenu() {
        while (true) {
            System.out.println("\n=== 人事经理菜单 ===");
            System.out.println("1. 添加操作员");
            System.out.println("2. 删除操作员");
            System.out.println("3. 查看所有系统事件");
            System.out.println("4. 系统事件查询");
            System.out.println("0. 退出");
            System.out.print("请选择操作: ");

            String choice = scanner.nextLine().trim();

            switch (choice) {
                case "1":
                    addOperator();
                    break;
                case "2":
                    deleteOperator();
                    break;
                case "3":
                    viewAllSystemEvents();
                    break;
                case "4":
                    querySystemEvents();
                    break;
                case "0":
                    return;
                default:
                    System.out.println("无效选择，请重新输入！");
            }
        }
    }
    
    /**
     * 操作员菜单
     */
    private void operatorMenu() {
        while (true) {
            System.out.println("\n=== 操作员菜单 ===");
            System.out.println("1. 系统事件查询（操作员事件）");
            System.out.println("0. 退出");
            System.out.print("请选择操作: ");

            String choice = scanner.nextLine().trim();

            switch (choice) {
                case "1":
                    viewOperatorEvents();
                    break;
                case "0":
                    return;
                default:
                    System.out.println("无效选择，请重新输入！");
            }
        }
    }
    
    /**
     * 添加操作员
     */
    private void addOperator() {
        System.out.println("\n=== 添加操作员 ===");
        System.out.print("请输入操作员用户名: ");
        String username = scanner.nextLine().trim();

        if (username.isEmpty()) {
            System.out.println("用户名不能为空！");
            return;
        }

        System.out.print("请输入操作员密码: ");
        String password = scanner.nextLine().trim();

        if (password.isEmpty()) {
            System.out.println("密码不能为空！");
            return;
        }

        String result = networkManager.addOperator(username, password, currentUser);

        switch (result) {
            case "SUCCESS":
                System.out.println("操作员添加成功！");
                break;
            case "USER_EXISTS":
                System.out.println("用户名已存在！");
                break;
            case "FAILED":
                System.out.println("添加操作员失败！");
                break;
            default:
                System.out.println("操作失败：" + result);
        }
    }
    
    /**
     * 删除操作员
     */
    private void deleteOperator() {
        System.out.println("\n=== 删除操作员 ===");
        System.out.print("请输入要删除的操作员用户名: ");
        String username = scanner.nextLine().trim();

        if (username.isEmpty()) {
            System.out.println("用户名不能为空！");
            return;
        }

        System.out.print("确认删除操作员 " + username + "？(y/n): ");
        String confirm = scanner.nextLine().trim().toLowerCase();

        if ("y".equals(confirm) || "yes".equals(confirm)) {
            if (networkManager.deleteOperator(username, currentUser)) {
                System.out.println("操作员删除成功！");
            } else {
                System.out.println("删除操作员失败！可能用户不存在或不是操作员。");
            }
        } else {
            System.out.println("取消删除操作。");
        }
    }
    
    /**
     * 查看所有系统事件
     */
    private void viewAllSystemEvents() {
        System.out.println("\n=== 所有系统事件 ===");
        List<SystemEvent> events = networkManager.getAllSystemEvents();

        if (events.isEmpty()) {
            System.out.println("暂无系统事件。");
        } else {
            System.out.println("共找到 " + events.size() + " 条系统事件：");
            System.out.println("----------------------------------------");
            for (SystemEvent event : events) {
                System.out.println(event.toString());
            }
        }
    }
    
    /**
     * 系统事件查询
     */
    private void querySystemEvents() {
        System.out.println("\n=== 系统事件查询 ===");
        System.out.println("1. 查询人事经理事件");
        System.out.println("2. 查询操作员事件");
        System.out.println("0. 返回");
        System.out.print("请选择查询类型: ");

        String choice = scanner.nextLine().trim();

        switch (choice) {
            case "1":
                viewEventsByRole("manager");
                break;
            case "2":
                viewEventsByRole("operator");
                break;
            case "0":
                return;
            default:
                System.out.println("无效选择！");
        }
    }
    
    /**
     * 根据角色查看事件
     */
    private void viewEventsByRole(String role) {
        String roleText = "manager".equals(role) ? "人事经理" : "操作员";
        System.out.println("\n=== " + roleText + "事件 ===");

        List<SystemEvent> events = networkManager.getSystemEventsByRole(role);

        if (events.isEmpty()) {
            System.out.println("暂无" + roleText + "事件。");
        } else {
            System.out.println("共找到 " + events.size() + " 条" + roleText + "事件：");
            System.out.println("----------------------------------------");
            for (SystemEvent event : events) {
                System.out.println(event.toString());
            }
        }
    }
    
    /**
     * 查看操作员事件（操作员专用）
     */
    private void viewOperatorEvents() {
        System.out.println("\n=== 操作员系统事件 ===");
        List<SystemEvent> events = networkManager.getOperatorEvents();

        if (events.isEmpty()) {
            System.out.println("暂无操作员事件。");
        } else {
            System.out.println("共找到 " + events.size() + " 条操作员事件：");
            System.out.println("----------------------------------------");
            for (SystemEvent event : events) {
                System.out.println(event.toString());
            }
        }
    }
    
    public static void main(String[] args) {
        Client client = new Client();
        client.start();
    }
}
