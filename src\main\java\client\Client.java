package client;

import server.SystemEvent;
import java.util.List;
import java.util.Scanner;

/**
 * Client Main Class
 */
public class Client {
    private NetworkManager networkManager;
    private Scanner scanner;
    private String currentUser;
    private String currentRole;
    
    public Client() {
        networkManager = new NetworkManager();
        scanner = new Scanner(System.in);
    }
    
    public void start() {
        System.out.println("=== HR Management System Client ===");
        
        // Connect to server
        if (!networkManager.connect()) {
            System.out.println("Unable to connect to server, program exiting.");
            return;
        }
        
        System.out.println("Connected to server");
        
        // Login process
        if (login()) {
            // Show different menus based on role
            if ("manager".equals(currentRole)) {
                managerMenu();
            } else if ("operator".equals(currentRole)) {
                operatorMenu();
            }
        }
        
        networkManager.disconnect();
        System.out.println("Disconnected, program ended.");
    }
    
    /**
     * Login process
     */
    private boolean login() {
        while (true) {
            System.out.println("\n=== Login ===");
            System.out.print("Enter username: ");
            String username = scanner.nextLine().trim();
            
            if (username.isEmpty()) {
                System.out.println("Username cannot be empty!");
                continue;
            }
            
            System.out.print("Enter password: ");
            String password = scanner.nextLine().trim();
            
            if (password.isEmpty()) {
                System.out.println("Password cannot be empty!");
                continue;
            }
            
            // Try to login
            String loginResult = networkManager.login(username, password);
            
            if (loginResult.startsWith("LOGIN_SUCCESS")) {
                String[] parts = loginResult.split("\\|");
                currentUser = username;
                currentRole = parts[1];
                System.out.println("Login successful! Welcome, " + 
                    ("manager".equals(currentRole) ? "Manager" : "Operator") + " " + username);
                return true;
            } else {
                // Login failed, check if user exists
                boolean userExists = networkManager.checkUserExists(username);
                
                if (!userExists) {
                    System.out.println("User does not exist!");
                } else {
                    System.out.println("Password incorrect!");
                    System.out.print("Do you want to change password? (y/n): ");
                    String choice = scanner.nextLine().trim().toLowerCase();
                    
                    if ("y".equals(choice) || "yes".equals(choice)) {
                        System.out.print("Enter new password: ");
                        String newPassword = scanner.nextLine().trim();
                        
                        if (!newPassword.isEmpty()) {
                            if (networkManager.updatePassword(username, newPassword)) {
                                System.out.println("Password updated successfully! Please login again.");
                            } else {
                                System.out.println("Password update failed!");
                            }
                        } else {
                            System.out.println("New password cannot be empty!");
                        }
                    }
                }
                
                System.out.print("Continue login? (y/n): ");
                String continueChoice = scanner.nextLine().trim().toLowerCase();
                if (!"y".equals(continueChoice) && !"yes".equals(continueChoice)) {
                    return false;
                }
            }
        }
    }
    
    /**
     * Manager menu
     */
    private void managerMenu() {
        while (true) {
            System.out.println("\n=== Manager Menu ===");
            System.out.println("1. Add Operator");
            System.out.println("2. Delete Operator");
            System.out.println("3. View All System Events");
            System.out.println("4. Query System Events");
            System.out.println("0. Exit");
            System.out.print("Please select operation: ");
            
            String choice = scanner.nextLine().trim();
            
            switch (choice) {
                case "1":
                    addOperator();
                    break;
                case "2":
                    deleteOperator();
                    break;
                case "3":
                    viewAllSystemEvents();
                    break;
                case "4":
                    querySystemEvents();
                    break;
                case "0":
                    return;
                default:
                    System.out.println("Invalid selection, please try again!");
            }
        }
    }
    
    /**
     * Operator menu
     */
    private void operatorMenu() {
        while (true) {
            System.out.println("\n=== Operator Menu ===");
            System.out.println("1. Query System Events (Operator Events)");
            System.out.println("0. Exit");
            System.out.print("Please select operation: ");
            
            String choice = scanner.nextLine().trim();
            
            switch (choice) {
                case "1":
                    viewOperatorEvents();
                    break;
                case "0":
                    return;
                default:
                    System.out.println("Invalid selection, please try again!");
            }
        }
    }
    
    /**
     * Add operator
     */
    private void addOperator() {
        System.out.println("\n=== Add Operator ===");
        System.out.print("Enter operator username: ");
        String username = scanner.nextLine().trim();
        
        if (username.isEmpty()) {
            System.out.println("Username cannot be empty!");
            return;
        }
        
        System.out.print("Enter operator password: ");
        String password = scanner.nextLine().trim();
        
        if (password.isEmpty()) {
            System.out.println("Password cannot be empty!");
            return;
        }
        
        String result = networkManager.addOperator(username, password, currentUser);
        
        switch (result) {
            case "SUCCESS":
                System.out.println("Operator added successfully!");
                break;
            case "USER_EXISTS":
                System.out.println("Username already exists!");
                break;
            case "FAILED":
                System.out.println("Failed to add operator!");
                break;
            default:
                System.out.println("Operation failed: " + result);
        }
    }
    
    /**
     * Delete operator
     */
    private void deleteOperator() {
        System.out.println("\n=== Delete Operator ===");
        System.out.print("Enter username to delete: ");
        String username = scanner.nextLine().trim();
        
        if (username.isEmpty()) {
            System.out.println("Username cannot be empty!");
            return;
        }
        
        System.out.print("Confirm delete operator " + username + "? (y/n): ");
        String confirm = scanner.nextLine().trim().toLowerCase();
        
        if ("y".equals(confirm) || "yes".equals(confirm)) {
            if (networkManager.deleteOperator(username, currentUser)) {
                System.out.println("Operator deleted successfully!");
            } else {
                System.out.println("Failed to delete operator! User may not exist or is not an operator.");
            }
        } else {
            System.out.println("Delete operation cancelled.");
        }
    }
    
    /**
     * View all system events
     */
    private void viewAllSystemEvents() {
        System.out.println("\n=== All System Events ===");
        List<SystemEvent> events = networkManager.getAllSystemEvents();
        
        if (events.isEmpty()) {
            System.out.println("No system events found.");
        } else {
            System.out.println("Found " + events.size() + " system events:");
            System.out.println("----------------------------------------");
            for (SystemEvent event : events) {
                System.out.println(event.toString());
            }
        }
    }
    
    /**
     * Query system events
     */
    private void querySystemEvents() {
        System.out.println("\n=== System Events Query ===");
        System.out.println("1. Query Manager Events");
        System.out.println("2. Query Operator Events");
        System.out.println("0. Return");
        System.out.print("Please select query type: ");
        
        String choice = scanner.nextLine().trim();
        
        switch (choice) {
            case "1":
                viewEventsByRole("manager");
                break;
            case "2":
                viewEventsByRole("operator");
                break;
            case "0":
                return;
            default:
                System.out.println("Invalid selection!");
        }
    }
    
    /**
     * View events by role
     */
    private void viewEventsByRole(String role) {
        String roleText = "manager".equals(role) ? "Manager" : "Operator";
        System.out.println("\n=== " + roleText + " Events ===");
        
        List<SystemEvent> events = networkManager.getSystemEventsByRole(role);
        
        if (events.isEmpty()) {
            System.out.println("No " + roleText + " events found.");
        } else {
            System.out.println("Found " + events.size() + " " + roleText + " events:");
            System.out.println("----------------------------------------");
            for (SystemEvent event : events) {
                System.out.println(event.toString());
            }
        }
    }
    
    /**
     * View operator events (for operators only)
     */
    private void viewOperatorEvents() {
        System.out.println("\n=== Operator System Events ===");
        List<SystemEvent> events = networkManager.getOperatorEvents();
        
        if (events.isEmpty()) {
            System.out.println("No operator events found.");
        } else {
            System.out.println("Found " + events.size() + " operator events:");
            System.out.println("----------------------------------------");
            for (SystemEvent event : events) {
                System.out.println(event.toString());
            }
        }
    }
    
    public static void main(String[] args) {
        Client client = new Client();
        client.start();
    }
}
