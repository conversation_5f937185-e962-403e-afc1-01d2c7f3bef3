@echo off
echo 正在编译人事管理系统...

REM Create output directory
if not exist "bin" mkdir bin

REM Check if MySQL driver exists
if exist "lib\mysql-connector-java-8.0.33.jar" (
    set CLASSPATH=lib/*
) else (
    echo 警告: lib目录中未找到MySQL驱动
    echo 请下载mysql-connector-java-8.0.33.jar到lib目录
    set CLASSPATH=.
)

REM Compile server
echo 编译服务端...
javac -d bin -cp "%CLASSPATH%" src/main/java/server/*.java
if %errorlevel% neq 0 (
    echo 服务端编译失败！
    pause
    exit /b 1
)

REM Compile client
echo 编译客户端...
javac -d bin -cp "%CLASSPATH%;bin" src/main/java/client/*.java
if %errorlevel% neq 0 (
    echo 客户端编译失败！
    pause
    exit /b 1
)

echo 编译完成！
echo.
echo 使用方法：
echo 1. 运行 start_server.bat 启动服务端
echo 2. 运行 start_client.bat 启动客户端
echo.
pause
