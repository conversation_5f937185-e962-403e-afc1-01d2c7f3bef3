@echo off
echo Compiling HR Management System...

REM Create output directory
if not exist "bin" mkdir bin

REM Check if MySQL driver exists
if exist "lib\mysql-connector-java-8.0.33.jar" (
    set CLASSPATH=lib/*
) else (
    echo Warning: MySQL driver not found in lib directory
    echo Please download mysql-connector-java-8.0.33.jar to lib directory
    set CLASSPATH=.
)

REM Compile server
echo Compiling server...
javac -d bin -cp "%CLASSPATH%" server/*.java
if %errorlevel% neq 0 (
    echo Server compilation failed!
    pause
    exit /b 1
)

REM Compile client
echo Compiling client...
javac -d bin -cp "%CLASSPATH%;bin" client/*.java
if %errorlevel% neq 0 (
    echo Client compilation failed!
    pause
    exit /b 1
)

echo Compilation completed!
echo.
echo Usage:
echo 1. Run start_server.bat to start server
echo 2. Run start_client.bat to start client
echo.
pause
