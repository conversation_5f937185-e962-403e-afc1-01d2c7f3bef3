package server;

import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;

/**
 * 服务端
 */
public class Server {
    private static final int PORT = 8888;
    private ServerSocket serverSocket;
    private boolean running = false;
    
    public void start() {
        try {
            serverSocket = new ServerSocket(PORT);
            running = true;
            System.out.println("人事管理系统服务端已启动，监听端口: " + PORT);
            System.out.println("等待客户端连接...");
            
            while (running) {
                Socket clientSocket = serverSocket.accept();
                System.out.println("客户端已连接: " + clientSocket.getInetAddress());

                // Create a handler thread for each client
                ServerHandler handler = new ServerHandler(clientSocket);
                handler.start();
            }
        } catch (IOException e) {
            if (running) {
                System.err.println("服务器错误: " + e.getMessage());
            }
        }
    }
    
    public void stop() {
        running = false;
        try {
            if (serverSocket != null) {
                serverSocket.close();
            }
        } catch (IOException e) {
            System.err.println("关闭服务器时出错: " + e.getMessage());
        }
    }
    
    public static void main(String[] args) {
        Server server = new Server();
        
        // Add shutdown hook to ensure server closes properly
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            System.out.println("\n正在关闭服务器...");
            server.stop();
        }));
        
        server.start();
    }
}
