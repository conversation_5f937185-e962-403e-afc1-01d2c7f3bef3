package client;

import server.SystemEvent;
import java.io.*;
import java.net.Socket;
import java.util.ArrayList;
import java.util.List;

/**
 * 网络通信管理类
 */
public class NetworkManager {
    private static final String SERVER_HOST = "localhost";
    private static final int SERVER_PORT = 8888;
    
    private Socket socket;
    private ObjectOutputStream out;
    private ObjectInputStream in;
    
    /**
     * 连接到服务器
     */
    public boolean connect() {
        try {
            socket = new Socket(SERVER_HOST, SERVER_PORT);
            out = new ObjectOutputStream(socket.getOutputStream());
            in = new ObjectInputStream(socket.getInputStream());
            return true;
        } catch (IOException e) {
            System.err.println("连接服务器失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 断开连接
     */
    public void disconnect() {
        try {
            if (socket != null && !socket.isClosed()) {
                socket.close();
            }
        } catch (IOException e) {
            System.err.println("断开连接时出错: " + e.getMessage());
        }
    }
    
    /**
     * 用户登录
     */
    public String login(String username, String password) {
        try {
            out.writeObject("LOGIN|" + username + "|" + password);
            String response = (String) in.readObject();
            return response;
        } catch (Exception e) {
            System.err.println("登录请求失败: " + e.getMessage());
            return "ERROR";
        }
    }
    
    /**
     * 检查用户是否存在
     */
    public boolean checkUserExists(String username) {
        try {
            out.writeObject("CHECK_USER|" + username);
            String response = (String) in.readObject();
            String[] parts = response.split("\\|");
            return Boolean.parseBoolean(parts[1]);
        } catch (Exception e) {
            System.err.println("检查用户失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 更新密码
     */
    public boolean updatePassword(String username, String newPassword) {
        try {
            out.writeObject("UPDATE_PASSWORD|" + username + "|" + newPassword);
            String response = (String) in.readObject();
            String[] parts = response.split("\\|");
            return "SUCCESS".equals(parts[1]);
        } catch (Exception e) {
            System.err.println("更新密码失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 添加操作员
     */
    public String addOperator(String username, String password, String managerName) {
        try {
            out.writeObject("ADD_OPERATOR|" + username + "|" + password + "|" + managerName);
            String response = (String) in.readObject();
            String[] parts = response.split("\\|");
            return parts[1];
        } catch (Exception e) {
            System.err.println("添加操作员失败: " + e.getMessage());
            return "ERROR";
        }
    }
    
    /**
     * 删除操作员
     */
    public boolean deleteOperator(String username, String managerName) {
        try {
            out.writeObject("DELETE_OPERATOR|" + username + "|" + managerName);
            String response = (String) in.readObject();
            String[] parts = response.split("\\|");
            return "SUCCESS".equals(parts[1]);
        } catch (Exception e) {
            System.err.println("删除操作员失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取所有系统事件
     */
    public List<SystemEvent> getAllSystemEvents() {
        try {
            out.writeObject("GET_ALL_EVENTS");
            String response = (String) in.readObject();
            String[] parts = response.split("\\|");
            int count = Integer.parseInt(parts[1]);
            
            List<SystemEvent> events = new ArrayList<>();
            for (int i = 0; i < count; i++) {
                SystemEvent event = (SystemEvent) in.readObject();
                events.add(event);
            }
            return events;
        } catch (Exception e) {
            System.err.println("获取系统事件失败: " + e.getMessage());
            return new ArrayList<>();
        }
    }
    
    /**
     * 根据角色获取系统事件
     */
    public List<SystemEvent> getSystemEventsByRole(String role) {
        try {
            out.writeObject("GET_EVENTS_BY_ROLE|" + role);
            String response = (String) in.readObject();
            String[] parts = response.split("\\|");
            int count = Integer.parseInt(parts[1]);
            
            List<SystemEvent> events = new ArrayList<>();
            for (int i = 0; i < count; i++) {
                SystemEvent event = (SystemEvent) in.readObject();
                events.add(event);
            }
            return events;
        } catch (Exception e) {
            System.err.println("获取系统事件失败: " + e.getMessage());
            return new ArrayList<>();
        }
    }
    
    /**
     * 获取操作员事件
     */
    public List<SystemEvent> getOperatorEvents() {
        try {
            out.writeObject("GET_OPERATOR_EVENTS");
            String response = (String) in.readObject();
            String[] parts = response.split("\\|");
            int count = Integer.parseInt(parts[1]);
            
            List<SystemEvent> events = new ArrayList<>();
            for (int i = 0; i < count; i++) {
                SystemEvent event = (SystemEvent) in.readObject();
                events.add(event);
            }
            return events;
        } catch (Exception e) {
            System.err.println("获取操作员事件失败: " + e.getMessage());
            return new ArrayList<>();
        }
    }
}
