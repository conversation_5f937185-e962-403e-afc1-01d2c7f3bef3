package server;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据库管理器
 */
public class DatabaseManager {
    private static final String DB_URL = "********************************************************************************************";
    private static final String DB_USER = "root";
    private static final String DB_PASSWORD = "root";
    
    static {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
    }
    
    /**
     * Get database connection
     */
    public static Connection getConnection() throws SQLException {
        return DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
    }
    
    /**
     * User authentication
     */
    public static User authenticateUser(String username, String password) {
        String sql = "SELECT * FROM users WHERE username = ? AND password = ?";
        try (Connection conn = getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, username);
            stmt.setString(2, password);
            
            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return new User(rs.getInt("id"), rs.getString("username"), 
                              rs.getString("password"), rs.getString("role"));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null;
    }
    
    /**
     * Check if user exists
     */
    public static boolean userExists(String username) {
        String sql = "SELECT COUNT(*) FROM users WHERE username = ?";
        try (Connection conn = getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, username);
            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return rs.getInt(1) > 0;
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * Update user password
     */
    public static boolean updatePassword(String username, String newPassword) {
        String sql = "UPDATE users SET password = ? WHERE username = ?";
        try (Connection conn = getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, newPassword);
            stmt.setString(2, username);
            
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * Add operator
     */
    public static boolean addOperator(String username, String password) {
        String sql = "INSERT INTO users (username, password, role) VALUES (?, ?, 'operator')";
        try (Connection conn = getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, username);
            stmt.setString(2, password);
            
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * Delete operator
     */
    public static boolean deleteOperator(String username) {
        String sql = "DELETE FROM users WHERE username = ? AND role = 'operator'";
        try (Connection conn = getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, username);
            
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * Log system event
     */
    public static void logSystemEvent(String description, String operatorUsername, String operatorRole) {
        String sql = "INSERT INTO system_events (event_description, operator_username, operator_role) VALUES (?, ?, ?)";
        try (Connection conn = getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, description);
            stmt.setString(2, operatorUsername);
            stmt.setString(3, operatorRole);
            
            stmt.executeUpdate();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
    
    /**
     * Get all system events
     */
    public static List<SystemEvent> getAllSystemEvents() {
        List<SystemEvent> events = new ArrayList<>();
        String sql = "SELECT * FROM system_events ORDER BY event_time DESC";
        
        try (Connection conn = getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                SystemEvent event = new SystemEvent(
                    rs.getInt("id"),
                    rs.getTimestamp("event_time"),
                    rs.getString("event_description"),
                    rs.getString("operator_username"),
                    rs.getString("operator_role")
                );
                events.add(event);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return events;
    }
    
    /**
     * Get system events by role
     */
    public static List<SystemEvent> getSystemEventsByRole(String role) {
        List<SystemEvent> events = new ArrayList<>();
        String sql = "SELECT * FROM system_events WHERE operator_role = ? ORDER BY event_time DESC";
        
        try (Connection conn = getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, role);
            ResultSet rs = stmt.executeQuery();
            
            while (rs.next()) {
                SystemEvent event = new SystemEvent(
                    rs.getInt("id"),
                    rs.getTimestamp("event_time"),
                    rs.getString("event_description"),
                    rs.getString("operator_username"),
                    rs.getString("operator_role")
                );
                events.add(event);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return events;
    }
    
    /**
     * Get operator system events
     */
    public static List<SystemEvent> getOperatorEvents() {
        return getSystemEventsByRole("operator");
    }
}
