-- 人事管理系统数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS hr_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE hr_system;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(100) NOT NULL,
    role ENUM('manager', 'operator') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建系统事件表
CREATE TABLE IF NOT EXISTS system_events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    event_description VARCHAR(500) NOT NULL,
    operator_username VARCHAR(50) NOT NULL,
    operator_role ENUM('manager', 'operator') NOT NULL,
    INDEX idx_operator_username (operator_username),
    INDEX idx_operator_role (operator_role),
    INDEX idx_event_time (event_time)
);

-- 插入默认的人事经理账户
INSERT INTO users (username, password, role) VALUES 
('admin', '123456', 'manager')
ON DUPLICATE KEY UPDATE password = '123456';

-- 插入初始系统事件
INSERT INTO system_events (event_description, operator_username, operator_role) VALUES
('系统初始化完成', 'system', 'manager');
