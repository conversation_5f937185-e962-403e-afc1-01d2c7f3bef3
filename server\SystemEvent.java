package server;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 系统事件实体类
 */
public class SystemEvent implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private int id;
    private Timestamp eventTime;
    private String eventDescription;
    private String operatorUsername;
    private String operatorRole;
    
    public SystemEvent() {}
    
    public SystemEvent(String eventDescription, String operatorUsername, String operatorRole) {
        this.eventDescription = eventDescription;
        this.operatorUsername = operatorUsername;
        this.operatorRole = operatorRole;
    }
    
    public SystemEvent(int id, Timestamp eventTime, String eventDescription, 
                      String operatorUsername, String operatorRole) {
        this.id = id;
        this.eventTime = eventTime;
        this.eventDescription = eventDescription;
        this.operatorUsername = operatorUsername;
        this.operatorRole = operatorRole;
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public Timestamp getEventTime() {
        return eventTime;
    }
    
    public void setEventTime(Timestamp eventTime) {
        this.eventTime = eventTime;
    }
    
    public String getEventDescription() {
        return eventDescription;
    }
    
    public void setEventDescription(String eventDescription) {
        this.eventDescription = eventDescription;
    }
    
    public String getOperatorUsername() {
        return operatorUsername;
    }
    
    public void setOperatorUsername(String operatorUsername) {
        this.operatorUsername = operatorUsername;
    }
    
    public String getOperatorRole() {
        return operatorRole;
    }
    
    public void setOperatorRole(String operatorRole) {
        this.operatorRole = operatorRole;
    }
    
    @Override
    public String toString() {
        return String.format("[%s] %s (操作者: %s - %s)", 
                eventTime, eventDescription, operatorUsername, operatorRole);
    }
}
