@echo off
echo Downloading MySQL Connector/J driver...
echo.

REM Try to download MySQL driver using PowerShell
powershell -Command "try { Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar' -OutFile 'lib/mysql-connector-java-8.0.33.jar' -ErrorAction Stop; Write-Host 'MySQL driver downloaded successfully!' } catch { Write-Host 'Download failed. Please download manually from:'; Write-Host 'https://dev.mysql.com/downloads/connector/j/'; Write-Host 'Or from Maven repository:'; Write-Host 'https://repo1.maven.org/maven2/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar'; Write-Host 'Save as: lib/mysql-connector-java-8.0.33.jar' }"

echo.
pause
