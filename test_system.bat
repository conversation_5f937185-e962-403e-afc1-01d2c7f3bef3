@echo off
echo Testing HR Management System...
echo.

echo Step 1: Testing database connection...
java -cp "bin;lib/*" server.DatabaseManager
if %errorlevel% neq 0 (
    echo Database connection test failed!
    echo Please ensure:
    echo 1. MySQL service is running
    echo 2. Database hr_system exists
    echo 3. sql/init.sql has been executed
    pause
    exit /b 1
)

echo.
echo Step 2: Testing server startup (will run for 5 seconds)...
timeout /t 5 /nobreak > nul
echo Server test completed.

echo.
echo System is ready to use!
echo.
echo To start the system:
echo 1. Run start_server.bat in one terminal
echo 2. Run start_client.bat in another terminal
echo 3. Login with: admin / admin123
echo.
pause
