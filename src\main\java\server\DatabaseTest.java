package server;

import java.sql.Connection;

/**
 * Database Connection Test
 */
public class DatabaseTest {
    public static void main(String[] args) {
        System.out.println("Testing database connection...");
        
        try {
            Connection conn = DatabaseManager.getConnection();
            if (conn != null && !conn.isClosed()) {
                System.out.println("SUCCESS: Database connection successful!");

                // Test if default admin user exists
                boolean adminExists = DatabaseManager.userExists("admin");
                if (adminExists) {
                    System.out.println("SUCCESS: Default admin user found!");
                } else {
                    System.out.println("ERROR: Default admin user not found!");
                    System.out.println("Please run sql/init.sql to initialize the database.");
                }

                conn.close();
            } else {
                System.out.println("ERROR: Database connection failed!");
            }
        } catch (Exception e) {
            System.out.println("ERROR: Database connection error: " + e.getMessage());
            System.out.println("\nPlease check:");
            System.out.println("1. MySQL service is running");
            System.out.println("2. Database 'hr_system' exists");
            System.out.println("3. Username/password in DatabaseManager.java is correct");
            System.out.println("4. sql/init.sql has been executed");
        }
    }
}
