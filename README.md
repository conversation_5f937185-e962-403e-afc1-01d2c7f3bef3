# 人事管理系统 (C/S架构)

基于Java的简单人事管理系统，采用客户端/服务端架构，使用MySQL数据库存储数据。

## 系统要求

- JDK 8+
- MySQL 8.0+
- MySQL Connector/J 8.0.33

## 功能特性

### 用户角色
- **人事经理 (manager)**: 具有完整的系统管理权限
- **操作员 (operator)**: 只能查询操作员相关的系统事件

### 主要功能

#### 人事经理功能
1. 添加/删除操作员
2. 查看所有系统事件
3. 系统事件查询（可选择查询人事经理或操作员事件）

#### 操作员功能
1. 系统事件查询（只能查询操作员事件）

#### 登录功能
- 用户名密码验证
- 用户不存在提示
- 密码错误时可选择修改密码

## 安装和使用

### 1. 数据库准备

1. 启动MySQL服务
2. 执行数据库初始化脚本：
```sql
mysql -u root -p < sql/init.sql
```

### 2. 下载MySQL驱动

下载MySQL Connector/J 8.0.33并放置到`lib/`目录：
```
lib/mysql-connector-java-8.0.33.jar
```

### 3. 编译项目

运行编译脚本：
```bash
compile.bat
```

### 4. 启动系统

1. **启动服务端**：
```bash
start_server.bat
```

2. **启动客户端**：
```bash
start_client.bat
```

## 默认账户

系统初始化后会创建一个默认的人事经理账户：
- 用户名: `admin`
- 密码: `admin123`
- 角色: 人事经理

## 数据库配置

如需修改数据库连接信息，请编辑 `server/DatabaseManager.java` 文件中的以下常量：
```java
private static final String DB_URL = "********************************************************************************************";
private static final String DB_USER = "root";
private static final String DB_PASSWORD = "root";
```

## 网络配置

默认服务端监听端口为8888，如需修改请编辑：
- `server/Server.java` 中的 `PORT` 常量
- `client/NetworkManager.java` 中的 `SERVER_PORT` 常量

## 项目结构

```
├── server/                 # 服务端代码
│   ├── Server.java        # 服务端主类
│   ├── ServerHandler.java # 客户端请求处理器
│   ├── DatabaseManager.java # 数据库管理
│   ├── User.java          # 用户实体类
│   └── SystemEvent.java   # 系统事件实体类
├── client/                # 客户端代码
│   ├── Client.java        # 客户端主类
│   └── NetworkManager.java # 网络通信管理
├── sql/                   # 数据库脚本
│   └── init.sql          # 数据库初始化脚本
├── lib/                   # 依赖库目录
├── bin/                   # 编译输出目录
├── compile.bat           # 编译脚本
├── start_server.bat      # 启动服务端脚本
└── start_client.bat      # 启动客户端脚本
```

## 使用说明

1. 确保MySQL服务运行并执行了初始化脚本
2. 先启动服务端，等待客户端连接
3. 启动客户端，使用默认账户或已创建的账户登录
4. 根据角色权限使用相应功能

## 注意事项

- 服务端必须先于客户端启动
- 确保防火墙允许8888端口通信
- 数据库连接信息需要根据实际环境调整
- 系统会自动记录所有操作的系统事件
